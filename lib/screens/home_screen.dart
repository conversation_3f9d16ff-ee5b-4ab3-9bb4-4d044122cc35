import 'dart:convert';
import 'dart:io';
import 'package:bitacora_template_generator/services/ai_service.dart';
import 'package:bitacora_template_generator/widgets/three_column_layout_widget.dart';
import 'package:bitacora_template_generator/widgets/auth_status_widget.dart';
import 'package:flutter/material.dart';

enum GenerationState {
  promptInput,
  generatingJson,
  jsonGenerated,
  generatingHtml,
  htmlGenerated,
  error,
}

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  String? _currentJsonTemplate;
  String? _currentHtmlTemplate;
  String? _currentPrompt;
  File? _currentAttachedFile;
  GenerationState _state = GenerationState.promptInput;
  late AnimationController _buttonAnimationController;

  @override
  void initState() {
    super.initState();
    _buttonAnimationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    // _state = GenerationState.generatingJson;
  }

  @override
  void dispose() {
    _buttonAnimationController.dispose();
    super.dispose();
  }

  Future<void> _onPromptSubmitted(String prompt, File? attachedFile) async {
    // Guardar el prompt y archivo para regeneración
    _currentPrompt = prompt;
    _currentAttachedFile = attachedFile;

    await _generateJsonFromPrompt();
  }

  Future<void> _generateJsonFromPrompt() async {
    if (_currentPrompt == null) return;

    setState(() {
      _state = GenerationState.generatingJson;
    });

    _buttonAnimationController.forward().then((_) {
      _buttonAnimationController.reverse();
    });

    try {
      final jsonTemplate =
          await AiService.generateJsonFromPrompt(_currentPrompt!, _currentAttachedFile);

      if (mounted) {
        if (jsonTemplate != null && jsonTemplate.isNotEmpty) {
          setState(() {
            _currentJsonTemplate = jsonTemplate;
            _currentHtmlTemplate = null; // Reset HTML when new JSON is generated
            _state = GenerationState.jsonGenerated;
          });
        } else {
          setState(() {
            _state = GenerationState.error;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _state = GenerationState.error;
        });
      }
    }
  }

  Future<void> _onGenerateHtml() async {
    if (_currentJsonTemplate == null) return;

    // Verificar que el JSON esté guardado en el servidor
    if (!_isJsonSavedToServer()) {
      // Mostrar mensaje de error
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Row(
            children: [
              Icon(Icons.warning, color: Colors.white),
              SizedBox(width: 8),
              Expanded(
                child: Text('Debe guardar el JSON en el servidor antes de generar el HTML'),
              ),
            ],
          ),
          backgroundColor: Colors.orange,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      );
      return;
    }

    await _generateHtmlFromJson();
  }

  Future<void> _generateHtmlFromJson() async {
    if (_currentJsonTemplate == null) return;

    setState(() {
      _state = GenerationState.generatingHtml;
    });

    try {
      final htmlTemplate = await AiService.generateHtmlTemplate(_currentJsonTemplate!);

      if (mounted) {
        if (htmlTemplate.isNotEmpty) {
          setState(() {
            _currentHtmlTemplate = htmlTemplate;
            _state = GenerationState.htmlGenerated;
          });
        } else {
          setState(() {
            _state = GenerationState.error;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _state = GenerationState.error;
        });
      }
    }
  }

  bool _isJsonSavedToServer() {
    if (_currentJsonTemplate == null) return false;

    try {
      final jsonObject = jsonDecode(_currentJsonTemplate!);
      // Verificar si tiene un ID del servidor (indicando que está guardado)
      return jsonObject.containsKey('id') && jsonObject['id'] != null;
    } catch (e) {
      return false;
    }
  }

  void _onJsonUpdated(String updatedJson) {
    setState(() {
      _currentJsonTemplate = updatedJson;
      // Reset HTML template since JSON has changed
      _currentHtmlTemplate = null;
      _state = GenerationState.jsonGenerated;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 30.0, top: 8.0),
            child: AuthStatusWidget(),
          ),
          Expanded(
            child: Stack(
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 8.0,
                  ),
                  child: _buildLayout(theme),
                ),
            
                // Floating loading overlay
                if (_state == GenerationState.generatingJson)
                  _buildFloatingLoadingOverlay(theme, 'Generando JSON...', 'La IA está analizando tu descripción'),
                if (_state == GenerationState.generatingHtml)
                  _buildFloatingLoadingOverlay(theme, 'Generando HTML...', 'Creando la plantilla HTML desde el JSON'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLayout(ThemeData theme) {
    return Stack(
      children: [
        // Layout principal
        ThreeColumnLayoutWidget(
          onPromptSubmitted: _onPromptSubmitted,
          jsonTemplate: _currentJsonTemplate,
          htmlTemplate: _currentHtmlTemplate,
          currentPrompt: _currentPrompt,
          currentAttachedFile: _currentAttachedFile,
          onGenerateHtml: _onGenerateHtml,
          onRegenerateJson: _generateJsonFromPrompt,
          onRegenerateHtml: _generateHtmlFromJson,
          showGenerateHtmlButton: _state == GenerationState.jsonGenerated && _currentHtmlTemplate == null,
          isJsonSavedToServer: _isJsonSavedToServer(),
          onClear: () {
            setState(() {
              _currentJsonTemplate = null;
              _currentHtmlTemplate = null;
              _currentPrompt = null;
              _currentAttachedFile = null;
              _state = GenerationState.promptInput;
            });
          },
          onJsonUpdated: _onJsonUpdated,
        ),
      ],
    );
  }

  Widget _buildFloatingLoadingOverlay(ThemeData theme, String title, String subtitle) {
    return Container(
      color: Colors.black.withValues(alpha: 0.3),
      child: Center(
        child: Card(
          elevation: 8,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            padding: const EdgeInsets.all(32),
            constraints: const BoxConstraints(maxWidth: 300),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(
                  width: 48,
                  height: 48,
                  child: CircularProgressIndicator(
                    strokeWidth: 4,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      theme.colorScheme.primary,
                    ),
                  ),
                ),
                const SizedBox(height: 24),
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  subtitle,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
