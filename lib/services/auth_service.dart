import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;

class AuthService {
  static const String _baseUrl = 'https://api.bitacora.io/api';
  static const String _loginEndpoint = '/sessions/login';

  // Credenciales por defecto
  static const String _defaultEmail = '<EMAIL>';
  static const String _defaultPassword = '12345678';

  // Token de sesión actual
  static String? _sessionToken;

  /// Obtiene el token de sesión actual
  static String? get sessionToken => _sessionToken;

  /// Verifica si hay una sesión activa
  static bool get isAuthenticated =>
      _sessionToken != null && _sessionToken!.isNotEmpty;

  /// Realiza el login con las credenciales por defecto
  static Future<bool> login() async {
    return await loginWithCredentials(_defaultEmail, _defaultPassword);
  }

  /// Realiza el login con credenciales específicas
  static Future<bool> loginWithCredentials(
      String email, String password) async {
    try {
      final url = Uri.parse('$_baseUrl$_loginEndpoint');

      final body = {
        'email': email,
        'password': password,
      };

      log('Intentando autenticación con: $email');

      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
        body: jsonEncode(body),
      );

      log('Respuesta de autenticación - Status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        // Extraer el session_token de la respuesta
        if (responseData.containsKey('session_token')) {
          _sessionToken = responseData['session_token'];
          log('Autenticación exitosa. Token obtenido.');
          return true;
        }
        log('Respuesta exitosa pero no se encontró session_token: ${response.body}');
        return false;
      } else {
        log('Error en autenticación: ${response.statusCode} - ${response.body}');
        return false;
      }
    } catch (e) {
      log('Excepción durante autenticación: $e');
      return false;
    }
  }

  /// Cierra la sesión actual
  static void logout() {
    _sessionToken = null;
    log('Sesión cerrada');
  }

  /// Obtiene los headers de autorización para otras peticiones
  static Map<String, String> getAuthHeaders() {
    final headers = <String, String>{
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (isAuthenticated) {
      headers['authorization'] = 'Token token=$_sessionToken';
    }
    return headers;
  }

  /// Inicializa la autenticación automáticamente al arrancar la app
  static Future<void> initialize() async {
    log('Inicializando servicio de autenticación...');
    final success = await login();
    if (success) {
      log('Autenticación automática exitosa');
    } else {
      log('Fallo en autenticación automática');
    }
  }

  /// Reintenta la autenticación si el token ha expirado
  static Future<bool> refreshAuthIfNeeded() async {
    log('Token inválido, reautenticando...');
    logout();
    return await login();
  }
}
