import 'dart:convert';
import 'dart:developer';
import 'package:http/http.dart' as http;
import 'package:bitacora_template_generator/services/auth_service.dart';

class TemplateService {
  static const String _baseUrl = 'https://api.bitacora.io/api';

  /// Guarda una plantilla JSON en el servidor y retorna la respuesta completa
  static Future<Map<String, dynamic>?> saveTemplate(
      String jsonTemplate, String organizationId) async {
    try {
      // Verificar que tenemos autenticación válida
      if (!AuthService.isAuthenticated) {
        log('No hay autenticación válida, intentando autenticar...');
        final success = await AuthService.refreshAuthIfNeeded();
        if (!success) {
          log('Fallo al obtener autenticación para guardar template');
          return null;
        }
      }

      final templatesEndpoint = '/organizations/$organizationId/templates';
      final url = Uri.parse('$_baseUrl$templatesEndpoint');

      // Parsear el JSON para enviarlo como objeto
      Map<String, dynamic> templateData;
      try {
        templateData = jsonDecode(jsonTemplate);
      } catch (e) {
        log('Error al parsear JSON template: $e');
        return null;
      }

      log('Guardando template en: $url');

      final response = await http.post(
        url,
        headers: AuthService.getAuthHeaders(),
        body: jsonEncode(templateData),
      );

      log('Respuesta de guardar template - Status: ${response.statusCode}');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = jsonDecode(response.body);
        log('Template guardado exitosamente');
        return responseData;
      } else {
        log('Error al guardar template: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      log('Excepción al guardar template: $e');
      return null;
    }
  }

  /// Obtiene la lista de templates de la organización
  static Future<List<Map<String, dynamic>>?> getTemplates(
      String organizationId) async {
    try {
      // Verificar que tenemos autenticación válida
      if (!AuthService.isAuthenticated) {
        log('No hay autenticación válida, intentando autenticar...');
        final success = await AuthService.refreshAuthIfNeeded();
        if (!success) {
          log('Fallo al obtener autenticación para obtener templates');
          return null;
        }
      }

      final templatesEndpoint = '/organizations/$organizationId/templates';
      final url = Uri.parse('$_baseUrl$templatesEndpoint');

      log('Obteniendo templates desde: $url');

      final response = await http.get(
        url,
        headers: AuthService.getAuthHeaders(),
      );

      log('Respuesta de obtener templates - Status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);

        // La respuesta puede ser una lista directa o estar dentro de un objeto 'data'
        if (responseData is List) {
          return List<Map<String, dynamic>>.from(responseData);
        } else if (responseData is Map && responseData.containsKey('data')) {
          return List<Map<String, dynamic>>.from(responseData['data']);
        } else if (responseData is Map &&
            responseData.containsKey('templates')) {
          return List<Map<String, dynamic>>.from(responseData['templates']);
        }

        log('Estructura de respuesta inesperada: ${response.body}');
        return null;
      } else {
        log('Error al obtener templates: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      log('Excepción al obtener templates: $e');
      return null;
    }
  }

  /// Obtiene un template específico por ID
  static Future<Map<String, dynamic>?> getTemplate(
      String organizationId, String templateId) async {
    try {
      // Verificar que tenemos autenticación válida
      if (!AuthService.isAuthenticated) {
        log('No hay autenticación válida, intentando autenticar...');
        final success = await AuthService.refreshAuthIfNeeded();
        if (!success) {
          log('Fallo al obtener autenticación para obtener template');
          return null;
        }
      }

      final templatesEndpoint = '/organizations/$organizationId/templates';
      final url = Uri.parse('$_baseUrl$templatesEndpoint/$templateId');

      log('Obteniendo template desde: $url');

      final response = await http.get(
        url,
        headers: AuthService.getAuthHeaders(),
      );

      log('Respuesta de obtener template - Status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        log('Template obtenido exitosamente');
        return responseData;
      } else {
        log('Error al obtener template: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      log('Excepción al obtener template: $e');
      return null;
    }
  }

  /// Actualiza un template existente
  static Future<Map<String, dynamic>?> updateTemplate(
      String organizationId, String templateId, String jsonTemplate) async {
    try {
      // Verificar que tenemos autenticación válida
      if (!AuthService.isAuthenticated) {
        log('No hay autenticación válida, intentando autenticar...');
        final success = await AuthService.refreshAuthIfNeeded();
        if (!success) {
          log('Fallo al obtener autenticación para actualizar template');
          return null;
        }
      }

      final templatesEndpoint = '/organizations/$organizationId/templates';
      final url = Uri.parse('$_baseUrl$templatesEndpoint/$templateId');

      // Parsear el JSON para enviarlo como objeto
      Map<String, dynamic> templateData;
      try {
        templateData = jsonDecode(jsonTemplate);
      } catch (e) {
        log('Error al parsear JSON template: $e');
        return null;
      }

      log('Actualizando template en: $url');

      final response = await http.put(
        url,
        headers: AuthService.getAuthHeaders(),
        body: jsonEncode(templateData),
      );

      log('Respuesta de actualizar template - Status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        log('Template actualizado exitosamente');
        return responseData;
      } else {
        log('Error al actualizar template: ${response.statusCode} - ${response.body}');
        return null;
      }
    } catch (e) {
      log('Excepción al actualizar template: $e');
      return null;
    }
  }
}
