import 'dart:io';
import 'package:bitacora_template_generator/widgets/prompt_input_widget.dart';
import 'package:bitacora_template_generator/widgets/generated_json_display_widget.dart';
import 'package:bitacora_template_generator/widgets/html_viewer_widget.dart';
import 'package:flutter/material.dart';

enum LayoutState {
  promptInput,
  promptAndJson,
  jsonAndHtml,
}

class ThreeColumnLayoutWidget extends StatefulWidget {
  final Function(String prompt, File? attachedFile) onPromptSubmitted;
  final String? jsonTemplate;
  final String? htmlTemplate;
  final String? currentPrompt;
  final File? currentAttachedFile;
  final VoidCallback? onGenerateHtml;
  final VoidCallback? onRegenerateJson;
  final VoidCallback? onRegenerateHtml;
  final VoidCallback onClear;
  final bool showGenerateHtmlButton;
  final bool isJsonSavedToServer;
  final Function(String)? onJsonUpdated;

  const ThreeColumnLayoutWidget({
    super.key,
    required this.onPromptSubmitted,
    this.jsonTemplate,
    this.htmlTemplate,
    this.currentPrompt,
    this.currentAttachedFile,
    this.onGenerateHtml,
    this.onRegenerateJson,
    this.onRegenerateHtml,
    required this.onClear,
    this.showGenerateHtmlButton = false,
    this.isJsonSavedToServer = false,
    this.onJsonUpdated,
  });

  @override
  State<ThreeColumnLayoutWidget> createState() => _ThreeColumnLayoutWidgetState();
}

class _ThreeColumnLayoutWidgetState extends State<ThreeColumnLayoutWidget> {
  LayoutState _currentLayout = LayoutState.promptInput;

  @override
  void initState() {
    super.initState();
    _updateLayoutState();
  }

  @override
  void didUpdateWidget(ThreeColumnLayoutWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    _updateLayoutState();
  }

  void _updateLayoutState() {
    LayoutState newState;

    if (widget.htmlTemplate != null) {
      newState = LayoutState.jsonAndHtml;
    } else if (widget.jsonTemplate != null) {
      newState = LayoutState.promptAndJson;
    } else {
      newState = LayoutState.promptInput;
    }

    if (_currentLayout != newState) {
      setState(() {
        _currentLayout = newState;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final screenWidth = MediaQuery.of(context).size.width;

    // Responsive layout: stack vertically on smaller screens
    if (screenWidth < 900) {
      return _buildVerticalLayout(theme);
    } else {
      return _buildHorizontalLayout(theme);
    }
  }

  Widget _buildHorizontalLayout(ThemeData theme) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // Left Column
        Expanded(
          flex: 1,
          child: Container(
            margin: const EdgeInsets.only(right: 12),
            child: _buildLeftColumn(theme),
          ),
        ),

        // Right Column
        Expanded(
          flex: 1,
          child: Container(
            margin: const EdgeInsets.only(left: 12),
            child: _buildRightColumn(theme),
          ),
        ),
      ],
    );
  }

  Widget _buildLeftColumn(ThemeData theme) {
    switch (_currentLayout) {
      case LayoutState.promptInput:
        return _buildInputSection(theme);
      case LayoutState.promptAndJson:
        return _buildInputSection(theme);
      case LayoutState.jsonAndHtml:
        // Si hay un prompt disponible, mostrarlo; si no, mostrar JSON
        return widget.currentPrompt != null
            ? _buildInputSection(theme)
            : _buildJsonSection(theme);
    }
  }

  Widget _buildRightColumn(ThemeData theme) {
    switch (_currentLayout) {
      case LayoutState.promptInput:
        return _buildPlaceholderSection(theme, 'JSON se mostrará aquí', Icons.data_object);
      case LayoutState.promptAndJson:
        return _buildJsonSection(theme);
      case LayoutState.jsonAndHtml:
        return _buildHtmlSection(theme);
    }
  }

  Widget _buildVerticalLayout(ThemeData theme) {
    return Column(
      children: [
        // Top Section
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: _buildLeftColumn(theme),
          ),
        ),

        // Bottom Section
        Expanded(
          child: Container(
            margin: const EdgeInsets.only(top: 12),
            child: _buildRightColumn(theme),
          ),
        ),
      ],
    );
  }

  Widget _buildPlaceholderSection(ThemeData theme, String message, IconData icon) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.1),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: theme.colorScheme.outline.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInputSection(ThemeData theme) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: theme.colorScheme.outline.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.edit_note,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Descripción de la Plantilla',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.onSurface,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),

              ],
            ),
          ),

          // Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: PromptInputWidget(
                onPromptSubmitted: widget.onPromptSubmitted,
                onClear: widget.onClear,
                currentPrompt: widget.currentPrompt,
                currentAttachedFile: widget.currentAttachedFile,
                onRegenerateJson: widget.onRegenerateJson,
                showRegenerateButton: widget.jsonTemplate != null,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildJsonSection(ThemeData theme) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: theme.colorScheme.outline.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.data_object,
                  color: theme.colorScheme.secondary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'JSON Generado',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.onSurface,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                // Navigation buttons
                if (_currentLayout == LayoutState.jsonAndHtml) ...[
                  OutlinedButton.icon(
                    onPressed: () {
                      setState(() {
                        _currentLayout = LayoutState.promptAndJson;
                      });
                    },
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: theme.colorScheme.primary),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    icon: Icon(Icons.chevron_left, size: 16, color: theme.colorScheme.primary),
                    label: Text(
                      'Ver Prompt',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
                // Regenerate JSON button
                if (widget.onRegenerateJson != null && widget.currentPrompt != null) ...[
                  const SizedBox(width: 12),
                  OutlinedButton.icon(
                    onPressed: widget.onRegenerateJson,
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: theme.colorScheme.secondary),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    icon: Icon(Icons.refresh, size: 16, color: theme.colorScheme.secondary),
                    label: Text(
                      'Regenerar JSON',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.secondary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
                // Generate HTML button
                if (widget.showGenerateHtmlButton && widget.onGenerateHtml != null) ...[
                  const SizedBox(width: 12),
                  OutlinedButton.icon(
                    onPressed: widget.isJsonSavedToServer ? widget.onGenerateHtml : null,
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(
                        color: widget.isJsonSavedToServer
                            ? theme.colorScheme.primary
                            : theme.colorScheme.outline,
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    icon: Icon(
                      Icons.chevron_right,
                      size: 16,
                      color: widget.isJsonSavedToServer
                          ? theme.colorScheme.primary
                          : theme.colorScheme.outline,
                    ),
                    label: Text(
                      widget.isJsonSavedToServer ? 'Generar HTML' : 'Guardar JSON primero',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: widget.isJsonSavedToServer
                            ? theme.colorScheme.primary
                            : theme.colorScheme.outline,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
                // View HTML button
                if (_currentLayout == LayoutState.promptAndJson && widget.htmlTemplate != null) ...[
                  const SizedBox(width: 12),
                  OutlinedButton.icon(
                    onPressed: () {
                      setState(() {
                        _currentLayout = LayoutState.jsonAndHtml;
                      });
                    },
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: theme.colorScheme.primary),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    icon: Icon(Icons.chevron_right, size: 16, color: theme.colorScheme.primary),
                    label: Text(
                      'Ver HTML',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: GeneratedJsonDisplayWidget(
                jsonString: widget.jsonTemplate!,
                onClear: widget.onClear,
                onJsonUpdated: widget.onJsonUpdated,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHtmlSection(ThemeData theme) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: theme.colorScheme.outline.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.web,
                  color: theme.colorScheme.tertiary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Plantilla HTML',
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.onSurface,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),

                // Navigation buttons
                if (widget.currentPrompt != null) ...[
                  OutlinedButton.icon(
                    onPressed: () {
                      setState(() {
                        _currentLayout = LayoutState.promptAndJson;
                      });
                    },
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: theme.colorScheme.primary),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    icon: Icon(Icons.chevron_left, size: 16, color: theme.colorScheme.primary),
                    label: Text(
                      'Ver JSON',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],

                // Regenerate HTML button
                if (widget.onRegenerateHtml != null) ...[
                  const SizedBox(width: 12),
                  OutlinedButton.icon(
                    onPressed: widget.onRegenerateHtml,
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: theme.colorScheme.secondary),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    ),
                    icon: Icon(Icons.refresh, size: 16, color: theme.colorScheme.secondary),
                    label: Text(
                      'Regenerar HTML',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.secondary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: HtmlViewerWidget(
                htmlCode: widget.htmlTemplate!,
                title: 'Plantilla Generada',
              ),
            ),
          ),
        ],
      ),
    );
  }
}
